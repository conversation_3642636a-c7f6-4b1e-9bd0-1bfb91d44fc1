import React, { useMemo } from 'react';
import {
  BlockView,
  ColorsV2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>per,
  <PERSON>ont<PERSON><PERSON>,
  HomeMovingProgressPostTaskType,
  IconImage,
  IFurnitureItem,
  ITypeFurnitureHomeMoving,
  ProcessButton,
  ScrollView,
  SizedBox,
  Spacing,
  getTextWithLocale,
  useAppStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { debounce } from 'lodash-es';

import { useAppNavigation, useI18n, usePostTaskHomeMoving } from '@hooks';

import { FurnitureItem } from './components/FurnitureItem';
import { OptionFurnitureItem } from './components/OptionFurnitureItem';
import { styles } from './styles';

export type ChooseFurnitureHomeMovingProps = {
  step: HomeMovingProgressPostTaskType;
};

export const ChooseFurnitureHomeMoving = ({ step }: ChooseFurnitureHomeMovingProps) => {
  const navigation = useAppNavigation();
  const { t, locale } = useI18n();
  const { getDetailSettingHomeMoving } = usePostTaskHomeMoving();

  const {
    oldHomeDetail,
    furniture,
    currentStep,
    setFurniture,
  } = usePostTaskStore();

  const furnitureSetting = useMemo(() => {
    return getDetailSettingHomeMoving(oldHomeDetail?.addressDetail?.taskPlace?.city)?.furniture;
  }, [getDetailSettingHomeMoving, oldHomeDetail?.addressDetail?.taskPlace?.city]);

  const onChangeQuantityItem = useMemo(
    () =>
      debounce((item: IFurnitureItem) => {
        const newFurniture = [...(furniture || [])];
        const index = newFurniture.findIndex((furnitureItem) => furnitureItem._id === item._id);
        if (index !== -1) {
          if (item.quantity === 0) {
            newFurniture.splice(index, 1);
          } else {
            newFurniture[index] = item;
          }
        } else if (item.quantity > 0) {
          newFurniture.push(item);
        }
        setFurniture(newFurniture);
      }, 300),
    [furniture, setFurniture],
  );

  const getQuantityItem = (item: IFurnitureItem) => {
    const furnitureItem = furniture?.find((furnitureItem) => furnitureItem._id === item._id);
    return furnitureItem?.quantity || 0;
  };

  const goToMovingProgress = () => {
    // trackingServiceView({
    //   screenName: TrackingScreenNames.ProcessIntroduction,
    //   serviceName: SERVICES.HOME_MOVING,
    //   entryPoint: TrackingScreenNames.FurnitureList,
    // });

    // Navigate to the global route for HomeMovingDescriptionProgress
    // @ts-ignore - This route exists in the main navigation but not in local RouteName
    navigation.navigate('HomeMovingDescriptionProgress');
  };

  if (currentStep !== step) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <ScrollView
        testID="ChooseFurnitureHomeMovingScrollView"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        <CText bold size={FontSizes.SIZE_18} style={styles.title}>
          {t('MOVING.CHOOSE_FURNITURE')}
        </CText>

        {furnitureSetting?.map((typeFurniture: ITypeFurnitureHomeMoving, index: number) => {
          return (
            <BlockView key={`type-furniture-${index}`} margin={{ top: Spacing.SPACE_24 }}>
              <CText
                bold
                size={FontSizes.SIZE_16}
                color={ColorsV2.neutral800}
                margin={{ bottom: Spacing.SPACE_16 }}
              >
                {getTextWithLocale(typeFurniture, locale)}
              </CText>

              {typeFurniture?.furniture?.map((furnitureItem: IFurnitureItem, furnitureIndex: number) => {
                const quantity = getQuantityItem(furnitureItem);
                return (
                  <FurnitureItem
                    key={`furniture-${furnitureIndex}`}
                    item={furnitureItem}
                    quantity={quantity}
                    onChangeQuantity={onChangeQuantityItem}
                    style={styles.furnitureItem}
                  />
                );
              })}

              {typeFurniture?.optionTypeFurniture?.map((option, optionIndex: number) => {
                return (
                  <OptionFurnitureItem
                    key={`option-${optionIndex}`}
                    option={option}
                    typeFurniture={typeFurniture}
                    style={styles.optionItem}
                  />
                );
              })}
            </BlockView>
          );
        })}

        <BlockView style={styles.oversizeSection}>
          <BlockView row center margin={{ bottom: Spacing.SPACE_12 }}>
            <IconImage
              name="package"
              size={24}
              color={ColorsV2.orange500}
              style={styles.oversizeIcon}
            />
            <CText
              bold
              size={FontSizes.SIZE_16}
              color={ColorsV2.neutral800}
              margin={{ left: Spacing.SPACE_08 }}
            >
              {t('MOVING.OVERSIZE_FURNITURE')}
            </CText>
          </BlockView>
          <CText
            size={FontSizes.SIZE_14}
            color={ColorsV2.neutral600}
            margin={{ bottom: Spacing.SPACE_16 }}
          >
            {t('MOVING.OVERSIZE_FURNITURE_DESCRIPTION')}
          </CText>
        </BlockView>

        <SizedBox height={30} />
        <ProcessButton
          label={t('MOVING.MOVING_PROCESS')}
          onPress={goToMovingProgress}
        />
      </ScrollView>
    </BlockView>
  );
};
