import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
  },
  contentContainer: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: Spacing.SPACE_24,
  },
  title: {
    color: ColorsV2.neutral800,
    marginTop: Spacing.SPACE_16,
  },
  furnitureItem: {
    marginBottom: Spacing.SPACE_12,
  },
  optionItem: {
    marginBottom: Spacing.SPACE_12,
  },
  oversizeSection: {
    backgroundColor: ColorsV2.neutral50,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_24,
  },
  oversizeIcon: {
    tintColor: ColorsV2.orange500,
  },
});
