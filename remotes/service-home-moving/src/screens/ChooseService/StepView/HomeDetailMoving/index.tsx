import React, { useEffect, useMemo, useRef } from 'react';
import PagerView from 'react-native-pager-view';
import {
  BlockView,
  BottomView,
  HomeMovingProgressPostTaskType,
  PrimaryButton,
  useUserStore,
} from '@btaskee/design-system';

import { LocationEmpty, ModalLogin } from '@components';
import { useAppNavigation, useI18n, usePostTaskHomeMoving } from '@hooks';

import { AddressDetailHomeMoving } from '../AddressDetailHomeMoving';
import { ChooseAddressMoving } from '../ChooseAddressMoving';
import { styles } from './styles';

export type HomeDetailMovingProps = {
  step: HomeMovingProgressPostTaskType;
};

export const HomeDetailMoving = ({ step }: HomeDetailMovingProps) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { getDataPostTaskMoving } = usePostTaskHomeMoving();
  const { user } = useUserStore();

  const pagerViewRef = useRef<PagerView>(null);
  const modalLoginRef = useRef<any>(null);

  const currentPage = useMemo(() => {
    const dataPostTask = getDataPostTaskMoving(step);
    return dataPostTask?.homeDetail?.addressDetail?.address ? 1 : 0;
  }, [getDataPostTaskMoving, step]);

  useEffect(() => {
    pagerViewRef.current?.setPage(currentPage);
  }, [currentPage]);

  if (!user?._id) {
    return (
      <>
        <BlockView flex>
          <BlockView flex>
            <LocationEmpty
              testID="notLoginHomeMovingTxt"
              label={
                t('MOVING.LOCATION_EMPTY') + '\n\n' + t('WELCOME_BACK_DES')
              }
            />
          </BlockView>
          <BottomView>
            <PrimaryButton
              testID="loginNowBtnHomeMoving"
              title={t('SIGN_UP_NOW')}
              onPress={() => {
                modalLoginRef.current?.open?.();
              }}
            />
          </BottomView>
        </BlockView>
        <ModalLogin
          ref={modalLoginRef}
          navigation={navigation}
        />
      </>
    );
  }

  return (
    <PagerView
      ref={pagerViewRef}
      initialPage={currentPage}
      style={styles.container}
      scrollEnabled={false}
    >
      <ChooseAddressMoving
        step={step}
        isFocused={currentPage === 0}
      />
      <AddressDetailHomeMoving step={step} />
    </PagerView>
  );
};
