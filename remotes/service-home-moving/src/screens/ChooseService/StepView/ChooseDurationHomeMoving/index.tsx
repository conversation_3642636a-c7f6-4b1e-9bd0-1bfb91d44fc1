import React, { useEffect, useMemo, useRef } from 'react';
import {
  AreaInput,
  BlockView,
  ColorsV2,
  CText,
  DatePicker,
  DateTimeHelpers,
  <PERSON>ceHelper,
  FontSizes,
  HomeMovingProgressPostTaskType,
  IDate,
  KeyboardAware,
  SizedBox,
  Spacing,
  TimePicker,
  useAppStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { get } from 'lodash-es';

import { Section } from '@components';
import { useI18n, usePostTaskHomeMoving } from '@hooks';

import { ConfirmChangeTimeModal, ConfirmChangeTimeModalHandle } from './components/ConfirmChangeTimeModal';
import { PriceIncrease } from './components/PriceIncrease';
import { styles } from './styles';

export type ChooseDurationHomeMovingProps = {
  step: HomeMovingProgressPostTaskType;
};

const SETTING_DEFAULT_TIME = {
  MOVING: 9,
  CLEANING_NEW_HOME: 9,
  MIN_HOUR_MOVING: 24, // giá trị được phép booking sau giờ hiện tại là 24
};

const LIMIT_DATE_HOME_MOVING = 30;

export const ChooseDurationHomeMoving = ({ step }: ChooseDurationHomeMovingProps) => {
  const { t } = useI18n();
  const { settings } = useAppStore();
  const { getIsPriceIncrease, getDateCleaningDefault } = usePostTaskHomeMoving();

  const {
    service,
    date: dateMoving,
    oldHomeDetail,
    newHomeDetail,
    timezones,
    setDate,
    setDuration,
    setNote,
  } = usePostTaskStore();

  const confirmChangeTimeModalRef = useRef<ConfirmChangeTimeModalHandle>(null);

  const defaultTaskTime = get(service, 'defaultTaskTime', SETTING_DEFAULT_TIME.MOVING);

  const postingLimits = useMemo(() => {
    let from = 0;
    let to = 0;
    const postingLimitsSetting = get(service, 'postingLimits');
    if (postingLimitsSetting) {
      from = DateTimeHelpers.getHourFromTime(postingLimitsSetting.from);
      to = DateTimeHelpers.getHourFromTime(postingLimitsSetting.to);
    }
    return {
      from,
      to,
    };
  }, [service]);

  const minDateMoving = useMemo(() => {
    // Sau giờ hiện tại 24h + 1 để hơn giờ hiện tại 1 tiếng và không cần check tới phút
    let minDate = DateTimeHelpers.toDayTz({ timezone: timezones?.oldHome })
      .add(SETTING_DEFAULT_TIME.MIN_HOUR_MOVING + 1, 'hours')
      .startOf('hours');

    // Nếu giờ hiện tại lớn hơn postingLimits.to(18h) set qua thêm 1 ngày nữa
    if (minDate.hour() >= postingLimits.to) {
      minDate = DateTimeHelpers.toDateTz({ timezone: timezones?.oldHome, date: minDate })
        .add(1, 'days')
        .hour(postingLimits.from)
        .startOf('hours');
    }
    return minDate;
  }, [postingLimits.from, postingLimits.to, timezones?.oldHome]);

  const rangeTimeMoving = useMemo(() => {
    let min = DateTimeHelpers.toDateTz({ timezone: timezones?.oldHome, date: minDateMoving });
    const max = DateTimeHelpers.toDateTz({ timezone: timezones?.oldHome, date: dateMoving })
      .hour(postingLimits.to)
      .startOf('hours');

    const isAfter = DateTimeHelpers.checkIsAfter({
      timezone: timezones?.oldHome,
      firstDate: dateMoving,
      secondDate: minDateMoving,
      unit: 'days',
    });
    if (isAfter) {
      min = DateTimeHelpers.toDateTz({ timezone: timezones?.oldHome, date: dateMoving })
        .hour(postingLimits.from)
        .startOf('minutes');
    }

    return {
      min,
      max,
    };
  }, [dateMoving, minDateMoving, postingLimits.from, postingLimits.to, timezones?.oldHome]);

  useEffect(() => {
    setDataDefault();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setDataDefault = () => {
    // Set duration mặc định
    setDuration(1);

    // Nếu chưa có date moving thì mới set date default
    if (!dateMoving) {
      // date time default là sau ngày 2 hiện tại 24h và 9h sáng
      const defaultDateTime = DateTimeHelpers.toDateTz({ timezone: timezones?.oldHome, date: minDateMoving }).toDate();
      if (minDateMoving.hour() <= defaultTaskTime) {
        defaultDateTime.setHours(defaultTaskTime);
      }
      onChangeDateTimeMoving(DateTimeHelpers.formatToString({ timezone: timezones?.oldHome, date: defaultDateTime }));
    }
  };

  const onChangeDateTimeMoving = async (value: IDate) => {
    const momentDateMoving = DateTimeHelpers.toDateTz({ timezone: timezones?.oldHome, date: dateMoving });
    // Nếu data không thay đổi thì không gọi action
    if (
      !value ||
      (momentDateMoving.isSame(value, 'year') &&
        momentDateMoving.isSame(value, 'month') &&
        momentDateMoving.isSame(value, 'day') &&
        momentDateMoving.isSame(value, 'hour') &&
        momentDateMoving.isSame(value, 'minute'))
    )
      return;

    let datetime = value;
    const isSameOrBefore = DateTimeHelpers.checkIsSameOrBefore({
      timezone: timezones?.oldHome,
      firstDate: value,
      secondDate: minDateMoving,
    });
    if (isSameOrBefore) {
      datetime = DateTimeHelpers.toDateTz({ timezone: timezones?.oldHome, date: minDateMoving });
    }
    if (oldHomeDetail?.isCleaningRequired || newHomeDetail?.isCleaningRequired) {
      const dateClean = getDateCleaningDefault(datetime);
      setTimeout(() => {
        if (!confirmChangeTimeModalRef?.current?.open) {
          return;
        }
        confirmChangeTimeModalRef.current?.open({
          oldHomeDetail: {
            isCleaningRequired: oldHomeDetail?.isCleaningRequired,
            date: dateClean?.old,
            timezone: timezones?.oldHome,
          },
          newHomeDetail: {
            isCleaningRequired: newHomeDetail?.isCleaningRequired,
            date: dateClean?.new,
            timezone: timezones?.newHome,
          },
          onConfirm: () => {
            setDate(datetime, service);
            // Handle cleaning dates if needed
          },
        });
      }, 500);
    } else {
      setDate(DateTimeHelpers.formatToString({ timezone: timezones?.oldHome, date: datetime }), service);
    }
  };

  if (step !== HomeMovingProgressPostTaskType.Step4) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <BlockView style={styles.content}>
        <KeyboardAware
          extraScrollHeight={Math.round(DeviceHelper.WINDOW.HEIGHT * 0.15)}
          enableOnAndroid={true}
          keyboardShouldPersistTaps="handled"
          testID="ChooseDurationHomeMovingScrollView"
          contentContainerStyle={styles.containerScroll}
          showsHorizontalScrollIndicator={false}
        >
          <CText bold size={FontSizes.SIZE_18} style={styles.txtLabel}>
            {t('MOVING.LABE_TIME_MOVING')}
          </CText>
          <BlockView>
            <DatePicker
              value={dateMoving}
              onChange={onChangeDateTimeMoving}
              settingSystem={settings?.settingSystem}
              minDay={DateTimeHelpers.toDateTz({ timezone: timezones?.oldHome, date: minDateMoving }).add(-1, 'hours')}
              limitDate={LIMIT_DATE_HOME_MOVING}
              noShowTitle
              timezone={timezones?.oldHome}
            />
            <TimePicker
              value={dateMoving}
              onChange={onChangeDateTimeMoving}
              settingSystem={settings?.settingSystem}
              minTime={rangeTimeMoving.min}
              maxTime={rangeTimeMoving.max}
              timezone={timezones?.oldHome}
              isDisabledMinimumTime
            />
          </BlockView>
          <PriceIncrease isShow={getIsPriceIncrease()?.moving} title={t('SUPPLY_DEMAND_COST_INCREASE')} />
          <SizedBox height={8} />
          <Section
            title={t('NOTES')}
            subTitle={t('TASK_NOTE_DESCRIPTION')}
            padding={{ horizontal: Spacing.SPACE_16 }}
            margin={{ top: Spacing.SPACE_24, bottom: Spacing.SPACE_16 }}
          >
            <SizedBox height={Spacing.SPACE_16} />
            <AreaInput
              placeholder={t('DISINFECTION_SERVICE_NOTE_CONTENT')}
              onChangeText={(value) => setNote(value)}
            />
          </Section>
        </KeyboardAware>
      </BlockView>
      <ConfirmChangeTimeModal ref={confirmChangeTimeModalRef} />
    </BlockView>
  );
};
