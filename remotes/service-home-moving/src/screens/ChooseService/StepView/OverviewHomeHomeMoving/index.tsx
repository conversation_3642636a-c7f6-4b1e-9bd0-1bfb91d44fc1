import React, { ReactNode } from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  DateWithGMT,
  FontSizes,
  HomeMovingProgressPostTaskType,
  HitSlop,
  IconImage,
  RowInfo,
  ScrollView,
  Spacing,
  TouchableOpacity,
  TypeFormatDate,
  getTextWithLocale,
  useAppStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { HomeDetailInfo, Section } from '@components';
import { useI18n, usePostTaskHomeMoving } from '@hooks';

import { styles } from './styles';

export type OverviewHomeHomeMovingProps = {
  step: HomeMovingProgressPostTaskType;
};

type BlockProps = {
  children: ReactNode;
  title: string;
  onEdit?: () => void;
};

const Block = ({ children, title, onEdit }: BlockProps) => {
  return (
    <Section
      title={title}
      leftTitle={
        <ConditionView
          condition={Boolean(onEdit)}
          viewTrue={
            <TouchableOpacity
              activeOpacity={0.7}
              hitSlop={HitSlop.MEDIUM}
              onPress={onEdit}
            >
              <IconImage
                name="edit"
                color={ColorsV2.orange500}
                size={22}
              />
            </TouchableOpacity>
          }
        />
      }
    >
      <BlockView style={styles.blockContainer}>{children}</BlockView>
    </Section>
  );
};

export const OverviewHomeHomeMoving = ({ step }: OverviewHomeHomeMovingProps) => {
  const { t, locale } = useI18n();
  const { onPressStep } = usePostTaskHomeMoving();

  const {
    oldHomeDetail,
    newHomeDetail,
    furniture,
    currentStep,
    timezones,
    date,
    note,
  } = usePostTaskStore();

  const onEdit = (stepEdit: HomeMovingProgressPostTaskType) => {
    onPressStep(stepEdit);
  };

  if (currentStep !== step) {
    return null;
  }

  return (
    <ScrollView
      testID="OverviewHomeMovingScrollView"
      showsVerticalScrollIndicator={false}
      style={styles.container}
      contentContainerStyle={styles.contentContainerStyle}
    >
      <Block title={t('ADDRESS')}>
        <HomeDetailInfo
          testIDs={{
            shortAddress: 'shortAddressTxtOldHomeDetail',
            homeType: 'homeTypeTxtOldHomeDetail',
          }}
          title={t('MOVING.PLACE_TO_MOVE')}
          fontSizeTitle={FontSizes.SIZE_16}
          homeDetail={oldHomeDetail || {}}
          onEdit={() => {
            onEdit(HomeMovingProgressPostTaskType.Step1);
            // trackingServiceClick({
            //   screenName: TrackingScreenNames.AdditionalServices,
            //   serviceName: SERVICES.HOME_MOVING,
            //   action: TRACKING_ACTION.EditCurrentLocation,
            // });
          }}
          isShowShortAddress
          isShowBorderBottom
        />
        <HomeDetailInfo
          testIDs={{
            shortAddress: 'shortAddressTxtNewHomeDetail',
            homeType: 'homeTypeTxtNewHomeDetail',
          }}
          title={t('MOVING.PLACE_TO_ARRIVE')}
          fontSizeTitle={FontSizes.SIZE_16}
          homeDetail={newHomeDetail || {}}
          onEdit={() => {
            onEdit(HomeMovingProgressPostTaskType.Step2);
            // trackingServiceClick({
            //   screenName: TrackingScreenNames.AdditionalServices,
            //   serviceName: SERVICES.HOME_MOVING,
            //   action: TRACKING_ACTION.EditNewLocation,
            // });
          }}
          isShowShortAddress
        />
      </Block>

      <Block
        title={t('MOVING.FURNITURE')}
        onEdit={() => onEdit(HomeMovingProgressPostTaskType.Step3)}
      >
        <ConditionView
          condition={Boolean(furniture?.length)}
          viewTrue={
            <BlockView>
              {furniture?.map((item, index) => (
                <RowInfo
                  key={`furniture-${index}`}
                  title={getTextWithLocale(item, locale)}
                  value={`${item.quantity}`}
                  titleStyle={styles.furnitureTitle}
                  valueStyle={styles.furnitureValue}
                />
              ))}
            </BlockView>
          }
          viewFalse={
            <CText
              size={FontSizes.SIZE_14}
              color={ColorsV2.neutral600}
            >
              {t('MOVING.NO_FURNITURE_SELECTED')}
            </CText>
          }
        />
      </Block>

      <Block
        title={t('MOVING.TIME_MOVING')}
        onEdit={() => onEdit(HomeMovingProgressPostTaskType.Step4)}
      >
        <ConditionView
          condition={Boolean(date)}
          viewTrue={
            <DateWithGMT
              date={date}
              timezone={timezones?.oldHome}
              typeFormat={TypeFormatDate.DATE_TIME_FULL}
              style={styles.dateText}
            />
          }
          viewFalse={
            <CText
              size={FontSizes.SIZE_14}
              color={ColorsV2.neutral600}
            >
              {t('MOVING.NO_TIME_SELECTED')}
            </CText>
          }
        />
      </Block>

      <ConditionView
        condition={Boolean(note)}
        viewTrue={
          <Block title={t('NOTES')}>
            <CText
              size={FontSizes.SIZE_14}
              color={ColorsV2.neutral800}
            >
              {note}
            </CText>
          </Block>
        }
      />
    </ScrollView>
  );
};
